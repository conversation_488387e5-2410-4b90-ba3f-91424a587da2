# Git Workflow - Boder Unity Project

## Branch Strategy

### Main Branches
- **`main`**: Production-ready code, stable releases
- **`develop`**: Integration branch for ongoing development
- **`release/milestone-X`**: Release preparation branches

### Feature Branches
- **`feature/[TaskID]-[brief-description]`**: Individual task development
- **`hotfix/[brief-description]`**: Critical bug fixes

## Branch Naming Convention

### Feature Branches
```
feature/P1-001-project-setup
feature/P1-004-player-movement
feature/P2-008-inventory-ui
feature/P3-005-save-system
```

### Hotfix Branches
```
hotfix/camera-clipping-fix
hotfix/audio-crash-android
hotfix/memory-leak-ui
```

### Release Branches
```
release/milestone-1-prototype
release/milestone-2-alpha
release/milestone-3-beta
```

## Workflow Steps

### 1. Starting New Task
```bash
# Switch to develop and pull latest
git checkout develop
git pull origin develop

# Create feature branch
git checkout -b feature/P1-001-project-setup

# Update task status in PROJECT_MANAGEMENT.md
# Change status from ❌ to 🔄
```

### 2. During Development
```bash
# Make changes and commit frequently
git add .
git commit -m "[P1-001] Initialize Git repository

- Created .gitignore for Unity
- Added .gitattributes for Git LFS
- Set up branch strategy documentation

Time spent: 2 hours
Progress: 50%
Testing: Manual"

# Push to remote feature branch
git push origin feature/P1-001-project-setup
```

### 3. Completing Task
```bash
# Final commit
git add .
git commit -m "[P1-001] Complete project setup

- Finalized Git configuration
- Updated project documentation
- Verified all files are properly tracked

Time spent: 4 hours
Progress: 100%
Testing: Manual
Closes: P1-001"

# Push final changes
git push origin feature/P1-001-project-setup
```

### 4. Code Review & Merge
```bash
# Create pull request (via GitHub/GitLab interface)
# After approval, merge to develop
git checkout develop
git pull origin develop
git merge feature/P1-001-project-setup
git push origin develop

# Delete feature branch
git branch -d feature/P1-001-project-setup
git push origin --delete feature/P1-001-project-setup
```

## Commit Message Format

### Standard Format
```
[TaskID] Brief description (50 characters max)

Detailed description of changes:
- Specific change 1
- Specific change 2
- Specific change 3

Time spent: X hours
Progress: X%
Testing: Manual/Automated/None
Closes: [TaskID] (if task is complete)
```

### Examples

**Feature Implementation**:
```
[P1-004] Implement basic player movement

- Added WASD input handling
- Integrated with Nappin Character Controller
- Added movement speed configuration
- Implemented ground detection

Time spent: 3 hours
Progress: 60%
Testing: Manual
```

**Bug Fix**:
```
[P1-015] Fix camera clipping through walls

- Added raycast collision detection
- Implemented camera position smoothing
- Fixed edge case with small colliders

Time spent: 1.5 hours
Progress: 100%
Testing: Manual
Closes: P1-015
```

**Documentation Update**:
```
[DOCS] Update development standards

- Added new coding conventions
- Updated Unity guidelines
- Clarified testing procedures

Time spent: 0.5 hours
Progress: 100%
Testing: None
```

## Code Review Process

### Pull Request Template
```markdown
## Pull Request: [P1-001] Project Setup & Version Control

**Type**: Setup/Feature/Bugfix/Hotfix
**Milestone**: Milestone 1 - Playable Prototype

### Changes Made
- Initialized Git repository with proper configuration
- Created .gitignore for Unity development
- Set up Git LFS for binary assets
- Documented Git workflow and branch strategy

### Testing Performed
- [x] Manual verification of Git configuration
- [x] Tested .gitignore excludes correct files
- [x] Verified Git LFS tracks binary files
- [ ] Automated tests (N/A for setup task)

### Dependencies
- None (first task)

### Blocks
- P1-002 (Unity Project Configuration)

### Checklist
- [x] Follows project standards
- [x] Documentation updated
- [x] No debug code left in
- [x] Task progress updated
- [x] Ready for team use
```

### Review Criteria
- [ ] Git configuration is correct
- [ ] .gitignore excludes appropriate files
- [ ] Git LFS is properly configured
- [ ] Documentation is clear and complete
- [ ] Branch strategy is well-defined
- [ ] Workflow is easy to follow

## Git LFS Setup

### Installation (Team Members)
```bash
# Install Git LFS (if not already installed)
git lfs install

# Track large files (already configured in .gitattributes)
git lfs track "*.png"
git lfs track "*.fbx"
# etc.

# Verify LFS is working
git lfs ls-files
```

### Verification
```bash
# Check which files are tracked by LFS
git lfs ls-files

# Check LFS status
git lfs status

# Verify file is stored in LFS
git lfs pointer --file="path/to/large/file.png"
```

## Emergency Procedures

### Hotfix Workflow
```bash
# Create hotfix from main
git checkout main
git pull origin main
git checkout -b hotfix/critical-bug-fix

# Make fix and test
# ... make changes ...

# Commit and push
git add .
git commit -m "[HOTFIX] Fix critical bug

- Description of fix
- Impact assessment
- Testing performed

Time spent: X hours
Testing: Manual"

git push origin hotfix/critical-bug-fix

# Merge to both main and develop
git checkout main
git merge hotfix/critical-bug-fix
git push origin main

git checkout develop
git merge hotfix/critical-bug-fix
git push origin develop

# Delete hotfix branch
git branch -d hotfix/critical-bug-fix
git push origin --delete hotfix/critical-bug-fix
```

### Rollback Procedures
```bash
# Rollback last commit (if not pushed)
git reset --hard HEAD~1

# Rollback specific file
git checkout HEAD~1 -- path/to/file

# Create revert commit (if already pushed)
git revert <commit-hash>
```

## Best Practices

### Do's
- ✅ Commit frequently with descriptive messages
- ✅ Include task ID in all commits
- ✅ Test changes before committing
- ✅ Update task progress regularly
- ✅ Use feature branches for all development
- ✅ Keep commits focused and atomic

### Don'ts
- ❌ Commit directly to main or develop
- ❌ Push broken code
- ❌ Commit large binary files without LFS
- ❌ Use generic commit messages
- ❌ Mix unrelated changes in one commit
- ❌ Force push to shared branches

---

*Git Workflow Version: 1.0*
*Last Updated: [Current Date]*
*Next Review: After Milestone 1*
