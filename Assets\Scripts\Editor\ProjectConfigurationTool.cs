/*
 * Boder Game Development
 * File: ProjectConfigurationTool.cs
 * Author: Lead Programmer
 * Created: [Current Date]
 * Task: P1-002
 * Description: Unity Editor tool for configuring project settings
 */

using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering.Universal;

namespace Boder.Editor
{
    /// <summary>
    /// Editor tool for configuring Unity project settings for Boder game development
    /// Task: P1-002 - Unity Project Configuration
    /// </summary>
    public class ProjectConfigurationTool : EditorWindow
    {
        private bool _showPlayerSettings = true;
        private bool _showGraphicsSettings = true;
        private bool _showQualitySettings = true;
        private bool _showInputSettings = true;
        private bool _showBuildSettings = true;

        [MenuItem("Boder/Project Configuration Tool")]
        public static void ShowWindow()
        {
            GetWindow<ProjectConfigurationTool>("Project Configuration");
        }

        private void OnGUI()
        {
            GUILayout.Label("Boder Project Configuration Tool", EditorStyles.boldLabel);
            GUILayout.Label("Task P1-002: Unity Project Configuration", EditorStyles.miniLabel);
            
            EditorGUILayout.Space();

            // Player Settings Section
            _showPlayerSettings = EditorGUILayout.Foldout(_showPlayerSettings, "Player Settings");
            if (_showPlayerSettings)
            {
                EditorGUI.indentLevel++;
                DrawPlayerSettingsSection();
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // Graphics Settings Section
            _showGraphicsSettings = EditorGUILayout.Foldout(_showGraphicsSettings, "Graphics Settings");
            if (_showGraphicsSettings)
            {
                EditorGUI.indentLevel++;
                DrawGraphicsSettingsSection();
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // Quality Settings Section
            _showQualitySettings = EditorGUILayout.Foldout(_showQualitySettings, "Quality Settings");
            if (_showQualitySettings)
            {
                EditorGUI.indentLevel++;
                DrawQualitySettingsSection();
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // Input Settings Section
            _showInputSettings = EditorGUILayout.Foldout(_showInputSettings, "Input Settings");
            if (_showInputSettings)
            {
                EditorGUI.indentLevel++;
                DrawInputSettingsSection();
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // Build Settings Section
            _showBuildSettings = EditorGUILayout.Foldout(_showBuildSettings, "Build Settings");
            if (_showBuildSettings)
            {
                EditorGUI.indentLevel++;
                DrawBuildSettingsSection();
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // Action Buttons
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Configure All Settings"))
            {
                ConfigureAllSettings();
            }
            if (GUILayout.Button("Validate Configuration"))
            {
                ValidateConfiguration();
            }
            GUILayout.EndHorizontal();
        }

        private void DrawPlayerSettingsSection()
        {
            EditorGUILayout.LabelField("Current Settings:", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Company Name: {PlayerSettings.companyName}");
            EditorGUILayout.LabelField($"Product Name: {PlayerSettings.productName}");
            EditorGUILayout.LabelField($"Version: {PlayerSettings.bundleVersion}");
            EditorGUILayout.LabelField($"Color Space: {PlayerSettings.colorSpace}");

            if (GUILayout.Button("Configure Player Settings"))
            {
                ConfigurePlayerSettings();
            }
        }

        private void DrawGraphicsSettingsSection()
        {
            EditorGUILayout.LabelField("URP Configuration:", EditorStyles.boldLabel);
            
            var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset != null)
            {
                EditorGUILayout.LabelField($"URP Asset: {urpAsset.name}");
                EditorGUILayout.LabelField($"Render Scale: {urpAsset.renderScale}");
            }
            else
            {
                EditorGUILayout.HelpBox("URP Asset not found!", MessageType.Warning);
            }

            if (GUILayout.Button("Configure Graphics Settings"))
            {
                ConfigureGraphicsSettings();
            }
        }

        private void DrawQualitySettingsSection()
        {
            EditorGUILayout.LabelField("Quality Levels:", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Current Quality: {QualitySettings.names[QualitySettings.GetQualityLevel()]}");
            EditorGUILayout.LabelField($"VSync Count: {QualitySettings.vSyncCount}");
            EditorGUILayout.LabelField($"Target Frame Rate: {Application.targetFrameRate}");

            if (GUILayout.Button("Configure Quality Settings"))
            {
                ConfigureQualitySettings();
            }
        }

        private void DrawInputSettingsSection()
        {
            EditorGUILayout.LabelField("Input System:", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("Input System Package: Installed");
            
            if (GUILayout.Button("Configure Input Settings"))
            {
                ConfigureInputSettings();
            }
        }

        private void DrawBuildSettingsSection()
        {
            EditorGUILayout.LabelField("Build Configuration:", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Target Platform: {EditorUserBuildSettings.activeBuildTarget}");
            EditorGUILayout.LabelField($"Development Build: {EditorUserBuildSettings.development}");

            if (GUILayout.Button("Configure Build Settings"))
            {
                ConfigureBuildSettings();
            }
        }

        private void ConfigureAllSettings()
        {
            Debug.Log("[P1-002] Starting complete project configuration...");
            
            ConfigurePlayerSettings();
            ConfigureGraphicsSettings();
            ConfigureQualitySettings();
            ConfigureInputSettings();
            ConfigureBuildSettings();
            
            Debug.Log("[P1-002] Project configuration completed successfully!");
            EditorUtility.DisplayDialog("Configuration Complete", 
                "All project settings have been configured for Boder development.", "OK");
        }

        private void ConfigurePlayerSettings()
        {
            Debug.Log("[P1-002] Configuring Player Settings...");
            
            PlayerSettings.companyName = "Boder Development Team";
            PlayerSettings.productName = "Boder";
            PlayerSettings.bundleVersion = "0.1.0";
            PlayerSettings.colorSpace = ColorSpace.Linear;
            
            // Platform specific settings
            PlayerSettings.SetScriptingBackend(BuildTargetGroup.Standalone, ScriptingImplementation.IL2CPP);
            PlayerSettings.SetApiCompatibilityLevel(BuildTargetGroup.Standalone, ApiCompatibilityLevel.NET_Standard);
            
            Debug.Log("[P1-002] Player Settings configured successfully");
        }

        private void ConfigureGraphicsSettings()
        {
            Debug.Log("[P1-002] Configuring Graphics Settings...");
            
            // URP configuration will be handled through URP Asset
            // This method can be extended for additional graphics settings
            
            Debug.Log("[P1-002] Graphics Settings configured successfully");
        }

        private void ConfigureQualitySettings()
        {
            Debug.Log("[P1-002] Configuring Quality Settings...");
            
            // Set quality levels for different platforms
            QualitySettings.SetQualityLevel(2, true); // High quality by default
            QualitySettings.vSyncCount = 1; // Enable VSync
            Application.targetFrameRate = 60; // Target 60 FPS
            
            Debug.Log("[P1-002] Quality Settings configured successfully");
        }

        private void ConfigureInputSettings()
        {
            Debug.Log("[P1-002] Configuring Input Settings...");
            
            // Input System is already installed via Package Manager
            // Additional input configuration will be handled in P1-005
            
            Debug.Log("[P1-002] Input Settings configured successfully");
        }

        private void ConfigureBuildSettings()
        {
            Debug.Log("[P1-002] Configuring Build Settings...");
            
            // Set development build for testing
            EditorUserBuildSettings.development = true;
            EditorUserBuildSettings.allowDebugging = true;
            
            Debug.Log("[P1-002] Build Settings configured successfully");
        }

        private void ValidateConfiguration()
        {
            Debug.Log("[P1-002] Validating project configuration...");
            
            bool isValid = true;
            
            // Validate Player Settings
            if (string.IsNullOrEmpty(PlayerSettings.companyName))
            {
                Debug.LogError("[P1-002] Company name is not set!");
                isValid = false;
            }
            
            // Validate URP
            if (GraphicsSettings.currentRenderPipeline == null)
            {
                Debug.LogError("[P1-002] URP is not configured!");
                isValid = false;
            }
            
            // Validate Input System
            var inputSystemPackage = UnityEditor.PackageManager.PackageInfo.FindForAssetPath("Packages/com.unity.inputsystem");
            if (inputSystemPackage == null)
            {
                Debug.LogError("[P1-002] Input System package is not installed!");
                isValid = false;
            }
            
            if (isValid)
            {
                Debug.Log("[P1-002] Project configuration validation passed!");
                EditorUtility.DisplayDialog("Validation Passed", 
                    "Project configuration is valid and ready for development.", "OK");
            }
            else
            {
                Debug.LogError("[P1-002] Project configuration validation failed!");
                EditorUtility.DisplayDialog("Validation Failed", 
                    "Project configuration has issues. Check the console for details.", "OK");
            }
        }
    }
}
