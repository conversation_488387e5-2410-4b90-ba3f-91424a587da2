/*
 * Boder Game Development
 * File: BuildConfigurationTool.cs
 * Author: Lead Programmer
 * Created: [Current Date]
 * Task: P1-002
 * Description: Build configuration tool for target platforms
 */

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace Boder.Editor
{
    /// <summary>
    /// Editor tool for configuring build settings for target platforms
    /// Task: P1-002 - Unity Project Configuration
    /// </summary>
    public class BuildConfigurationTool : EditorWindow
    {
        private BuildTarget _selectedPlatform = BuildTarget.StandaloneWindows64;
        private bool _developmentBuild = true;
        private bool _autoconnectProfiler = false;
        private bool _deepProfiling = false;
        private bool _scriptDebugging = true;

        [MenuItem("Boder/Build Configuration Tool")]
        public static void ShowWindow()
        {
            GetWindow<BuildConfigurationTool>("Build Configuration");
        }

        private void OnGUI()
        {
            GUILayout.Label("Build Configuration Tool", EditorStyles.boldLabel);
            GUILayout.Label("Task P1-002: Unity Project Configuration", EditorStyles.miniLabel);
            
            EditorGUILayout.Space();

            // Platform Selection
            DrawPlatformSelection();
            
            EditorGUILayout.Space();

            // Build Options
            DrawBuildOptions();
            
            EditorGUILayout.Space();

            // Current Build Settings
            DrawCurrentBuildSettings();
            
            EditorGUILayout.Space();

            // Action Buttons
            DrawActionButtons();
        }

        private void DrawPlatformSelection()
        {
            EditorGUILayout.LabelField("Target Platform", EditorStyles.boldLabel);
            
            _selectedPlatform = (BuildTarget)EditorGUILayout.EnumPopup("Platform", _selectedPlatform);
            
            // Platform-specific information
            switch (_selectedPlatform)
            {
                case BuildTarget.StandaloneWindows64:
                    EditorGUILayout.HelpBox("PC (Windows 64-bit) - Primary development platform", MessageType.Info);
                    break;
                case BuildTarget.Android:
                    EditorGUILayout.HelpBox("Android - Mobile platform with performance optimizations", MessageType.Info);
                    break;
                case BuildTarget.iOS:
                    EditorGUILayout.HelpBox("iOS - Mobile platform with Apple-specific optimizations", MessageType.Info);
                    break;
                default:
                    EditorGUILayout.HelpBox("Platform not specifically configured for Boder development", MessageType.Warning);
                    break;
            }
        }

        private void DrawBuildOptions()
        {
            EditorGUILayout.LabelField("Build Options", EditorStyles.boldLabel);
            
            _developmentBuild = EditorGUILayout.Toggle("Development Build", _developmentBuild);
            
            if (_developmentBuild)
            {
                EditorGUI.indentLevel++;
                _autoconnectProfiler = EditorGUILayout.Toggle("Autoconnect Profiler", _autoconnectProfiler);
                _deepProfiling = EditorGUILayout.Toggle("Deep Profiling", _deepProfiling);
                _scriptDebugging = EditorGUILayout.Toggle("Script Debugging", _scriptDebugging);
                EditorGUI.indentLevel--;
            }
        }

        private void DrawCurrentBuildSettings()
        {
            EditorGUILayout.LabelField("Current Build Settings", EditorStyles.boldLabel);
            
            EditorGUILayout.LabelField($"Active Platform: {EditorUserBuildSettings.activeBuildTarget}");
            EditorGUILayout.LabelField($"Development Build: {EditorUserBuildSettings.development}");
            EditorGUILayout.LabelField($"Autoconnect Profiler: {EditorUserBuildSettings.connectProfiler}");
            EditorGUILayout.LabelField($"Deep Profiling: {EditorUserBuildSettings.buildWithDeepProfilingSupport}");
            EditorGUILayout.LabelField($"Script Debugging: {EditorUserBuildSettings.allowDebugging}");
            
            // Scenes in build
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Scenes in Build:", EditorStyles.boldLabel);
            var scenes = EditorBuildSettings.scenes;
            if (scenes.Length == 0)
            {
                EditorGUILayout.HelpBox("No scenes added to build settings!", MessageType.Warning);
            }
            else
            {
                foreach (var scene in scenes)
                {
                    string status = scene.enabled ? "✓" : "✗";
                    EditorGUILayout.LabelField($"{status} {scene.path}");
                }
            }
        }

        private void DrawActionButtons()
        {
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Configure for PC"))
            {
                ConfigureForPC();
            }
            if (GUILayout.Button("Configure for Mobile"))
            {
                ConfigureForMobile();
            }
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Apply Settings"))
            {
                ApplyBuildSettings();
            }
            if (GUILayout.Button("Add Scenes to Build"))
            {
                AddScenesToBuild();
            }
            GUILayout.EndHorizontal();

            EditorGUILayout.Space();

            if (GUILayout.Button("Validate Build Configuration"))
            {
                ValidateBuildConfiguration();
            }
        }

        private void ConfigureForPC()
        {
            Debug.Log("[P1-002] Configuring build settings for PC...");
            
            _selectedPlatform = BuildTarget.StandaloneWindows64;
            _developmentBuild = true;
            _autoconnectProfiler = true;
            _deepProfiling = false;
            _scriptDebugging = true;
            
            // PC-specific player settings
            PlayerSettings.SetScriptingBackend(BuildTargetGroup.Standalone, ScriptingImplementation.IL2CPP);
            PlayerSettings.SetApiCompatibilityLevel(BuildTargetGroup.Standalone, ApiCompatibilityLevel.NET_Standard);
            PlayerSettings.Standalone.resizableWindow = true;
            PlayerSettings.Standalone.fullscreenMode = FullScreenMode.Windowed;
            PlayerSettings.defaultScreenWidth = 1920;
            PlayerSettings.defaultScreenHeight = 1080;
            
            Debug.Log("[P1-002] PC build configuration completed");
        }

        private void ConfigureForMobile()
        {
            Debug.Log("[P1-002] Configuring build settings for Mobile...");
            
            _selectedPlatform = BuildTarget.Android; // Default to Android
            _developmentBuild = false; // Release builds for mobile
            _autoconnectProfiler = false;
            _deepProfiling = false;
            _scriptDebugging = false;
            
            // Android-specific settings
            if (_selectedPlatform == BuildTarget.Android)
            {
                PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
                PlayerSettings.SetApiCompatibilityLevel(BuildTargetGroup.Android, ApiCompatibilityLevel.NET_Standard);
                PlayerSettings.Android.targetSdkVersion = AndroidSdkVersions.AndroidApiLevel30;
                PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel23;
                PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARM64;
            }
            
            Debug.Log("[P1-002] Mobile build configuration completed");
        }

        private void ApplyBuildSettings()
        {
            Debug.Log($"[P1-002] Applying build settings for {_selectedPlatform}...");
            
            // Switch platform if needed
            if (EditorUserBuildSettings.activeBuildTarget != _selectedPlatform)
            {
                var buildTargetGroup = BuildPipeline.GetBuildTargetGroup(_selectedPlatform);
                EditorUserBuildSettings.SwitchActiveBuildTarget(buildTargetGroup, _selectedPlatform);
            }
            
            // Apply build options
            EditorUserBuildSettings.development = _developmentBuild;
            EditorUserBuildSettings.connectProfiler = _autoconnectProfiler;
            EditorUserBuildSettings.buildWithDeepProfilingSupport = _deepProfiling;
            EditorUserBuildSettings.allowDebugging = _scriptDebugging;
            
            Debug.Log("[P1-002] Build settings applied successfully");
        }

        private void AddScenesToBuild()
        {
            Debug.Log("[P1-002] Adding scenes to build settings...");
            
            var sceneList = new List<EditorBuildSettingsScene>();
            
            // Add main scenes (these will be created in later tasks)
            string[] scenePaths = {
                "Assets/Scenes/00_MainMenu.unity",
                "Assets/Scenes/01_GameplayTest.unity"
            };
            
            foreach (string scenePath in scenePaths)
            {
                var scene = new EditorBuildSettingsScene(scenePath, true);
                sceneList.Add(scene);
            }
            
            EditorBuildSettings.scenes = sceneList.ToArray();
            
            Debug.Log($"[P1-002] Added {sceneList.Count} scenes to build settings");
        }

        private void ValidateBuildConfiguration()
        {
            Debug.Log("[P1-002] Validating build configuration...");
            
            bool isValid = true;
            
            // Check if scenes are added
            if (EditorBuildSettings.scenes.Length == 0)
            {
                Debug.LogWarning("[P1-002] No scenes in build settings");
                // This is not critical for initial setup
            }
            
            // Check platform-specific settings
            var buildTargetGroup = BuildPipeline.GetBuildTargetGroup(EditorUserBuildSettings.activeBuildTarget);
            
            // Validate scripting backend
            var scriptingBackend = PlayerSettings.GetScriptingBackend(buildTargetGroup);
            if (scriptingBackend != ScriptingImplementation.IL2CPP)
            {
                Debug.LogWarning($"[P1-002] Scripting backend is {scriptingBackend}, recommended: IL2CPP");
            }
            
            // Validate API compatibility
            var apiLevel = PlayerSettings.GetApiCompatibilityLevel(buildTargetGroup);
            if (apiLevel != ApiCompatibilityLevel.NET_Standard)
            {
                Debug.LogWarning($"[P1-002] API compatibility is {apiLevel}, recommended: .NET Standard");
            }
            
            // Platform-specific validation
            switch (EditorUserBuildSettings.activeBuildTarget)
            {
                case BuildTarget.Android:
                    if (PlayerSettings.Android.minSdkVersion < AndroidSdkVersions.AndroidApiLevel23)
                    {
                        Debug.LogWarning("[P1-002] Android min SDK version is below API 23");
                    }
                    break;
            }
            
            if (isValid)
            {
                Debug.Log("[P1-002] Build configuration validation passed!");
                EditorUtility.DisplayDialog("Build Validation Passed", 
                    "Build configuration is valid for target platforms.", "OK");
            }
            else
            {
                Debug.LogWarning("[P1-002] Build configuration has warnings. Check console for details.");
                EditorUtility.DisplayDialog("Build Validation Completed", 
                    "Build configuration validation completed with warnings. Check console for details.", "OK");
            }
        }
    }
}
