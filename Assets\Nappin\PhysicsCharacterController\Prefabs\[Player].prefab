%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6212462410187470976
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6212462410187470983}
  m_Layer: 8
  m_Name: Mesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6212462410187470983
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6212462410187470976}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 5890040739969943047}
  - {fileID: 5890040740529091201}
  m_Father: {fileID: 6212462410201203768}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6212462410201203770
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6212462410201203768}
  - component: {fileID: 6212462410201203774}
  - component: {fileID: 6212462410201203769}
  - component: {fileID: 6212462410201203773}
  m_Layer: 8
  m_Name: '[Player]'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6212462410201203768
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6212462410201203770}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 6212462410187470983}
  - {fileID: 6212462411298747933}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &6212462410201203774
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6212462410201203770}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &6212462410201203769
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6212462410201203770}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 112
  m_CollisionDetection: 0
--- !u!114 &6212462410201203773
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6212462410201203770}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d653000bcba62c14ba9b6c5c324a4197, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  groundMask:
    serializedVersion: 2
    m_Bits: 512
  movementSpeed: 14.4
  movementThrashold: 0.01
  dampSpeedUp: 0.2
  dampSpeedDown: 0.1
  jumpVelocity: 20
  fallMultiplier: 1.7
  holdJumpMultiplier: 5
  frictionAgainstFloor: 0.189
  frictionAgainstWall: 0.082
  canLongJump: 1
  groundCheckerThrashold: 0.1
  slopeCheckerThrashold: 0.51
  stepCheckerThrashold: 0.6
  maxClimbableSlopeAngle: 53.6
  maxStepHeight: 0.74
  speedMultiplierOnAngle:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  canSlideMultiplierCurve: 0.061
  cantSlideMultiplierCurve: 0.039
  climbingStairsMultiplierCurve: 0.086
  gravityMultiplier: 8
  gravityMultiplyerOnSlideChange: 2
  gravityMultiplierIfUnclimbableSlope: 30
  lockOnSlope: 1
  wallCheckerThrashold: 0.8
  hightWallCheckerChecker: 0.5
  jumpFromWallMultiplier: 31
  multiplierVerticalLeap: 1
  sprintSpeed: 20
  crouchHeightMultiplier: 0.5
  POV_normalHeadHeight: {x: 0, y: 0.5, z: -0.1}
  POV_crouchHeadHeight: {x: 0, y: -0.1, z: -0.1}
  characterCamera: {fileID: 0}
  characterModel: {fileID: 6212462410187470976}
  characterModelRotationSmooth: 0.1
  meshCharacter: {fileID: 6554549952702375995}
  meshCharacterCrouch: {fileID: 6554549952155786429}
  headPoint: {fileID: 6212462411298747933}
  input: {fileID: 0}
  debug: 1
  OnJump:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_MethodName: ParticleJump
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  minimumVerticalSpeedToLandEvent: 0.5
  OnLand:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_MethodName: ParticleLand
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  minimumHorizontalSpeedToFastEvent: 20
  OnFast:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_MethodName: ParticleFast
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  OnWallSlide:
    m_PersistentCalls:
      m_Calls: []
  OnSprint:
    m_PersistentCalls:
      m_Calls: []
  OnCrouch:
    m_PersistentCalls:
      m_Calls: []
  targetAngle: 47.939434
--- !u!1 &6212462411298747934
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6212462411298747933}
  m_Layer: 8
  m_Name: HeadPoint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6212462411298747933
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6212462411298747934}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.5, z: -0.1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 6212462410201203768}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1010163671241760268
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 6212462410187470983}
    m_Modifications:
    - target: {fileID: 6121134019321951799, guid: 93b935bc5ccb53c47a8897a194af1e3a,
        type: 3}
      propertyPath: m_Name
      value: character
      objectReference: {fileID: 0}
    - target: {fileID: 6897670030982261901, guid: 93b935bc5ccb53c47a8897a194af1e3a,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6897670030982261901, guid: 93b935bc5ccb53c47a8897a194af1e3a,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6897670030982261901, guid: 93b935bc5ccb53c47a8897a194af1e3a,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6897670030982261901, guid: 93b935bc5ccb53c47a8897a194af1e3a,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6897670030982261901, guid: 93b935bc5ccb53c47a8897a194af1e3a,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6897670030982261901, guid: 93b935bc5ccb53c47a8897a194af1e3a,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6897670030982261901, guid: 93b935bc5ccb53c47a8897a194af1e3a,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6897670030982261901, guid: 93b935bc5ccb53c47a8897a194af1e3a,
        type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6897670030982261901, guid: 93b935bc5ccb53c47a8897a194af1e3a,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6897670030982261901, guid: 93b935bc5ccb53c47a8897a194af1e3a,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6897670030982261901, guid: 93b935bc5ccb53c47a8897a194af1e3a,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 93b935bc5ccb53c47a8897a194af1e3a, type: 3}
--- !u!1 &6554549952702375995 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6121134019321951799, guid: 93b935bc5ccb53c47a8897a194af1e3a,
    type: 3}
  m_PrefabInstance: {fileID: 1010163671241760268}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &5890040740529091201 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 6897670030982261901, guid: 93b935bc5ccb53c47a8897a194af1e3a,
    type: 3}
  m_PrefabInstance: {fileID: 1010163671241760268}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6218734480327160079
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 6212462410187470983}
    m_Modifications:
    - target: {fileID: 572171383063410440, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 572171383063410440, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 572171383063410440, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 572171383063410440, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 572171383063410440, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 572171383063410440, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 572171383063410440, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 572171383063410440, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 572171383063410440, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 572171383063410440, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 572171383063410440, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 917347272827809202, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_Name
      value: character_small
      objectReference: {fileID: 0}
    - target: {fileID: 917347272827809202, guid: 4990112ecf119944baa60b7f6690e8e4,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 4990112ecf119944baa60b7f6690e8e4, type: 3}
--- !u!1 &6554549952155786429 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 917347272827809202, guid: 4990112ecf119944baa60b7f6690e8e4,
    type: 3}
  m_PrefabInstance: {fileID: 6218734480327160079}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &5890040739969943047 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 572171383063410440, guid: 4990112ecf119944baa60b7f6690e8e4,
    type: 3}
  m_PrefabInstance: {fileID: 6218734480327160079}
  m_PrefabAsset: {fileID: 0}
