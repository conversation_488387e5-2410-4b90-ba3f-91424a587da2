{"name": "Boder.Editor", "rootNamespace": "Boder.Editor", "references": ["Boder.Runtime", "Nappin.PhysicsCharacterController", "Unity.Cinemachine", "Unity.InputSystem", "Unity.RenderPipelines.Universal.Runtime", "Unity.RenderPipelines.Universal.Editor"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}