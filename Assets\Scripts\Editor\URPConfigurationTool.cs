/*
 * Boder Game Development
 * File: URPConfigurationTool.cs
 * Author: Lead Programmer
 * Created: [Current Date]
 * Task: P1-002
 * Description: URP configuration tool for optimal performance
 */

using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering.Universal;

namespace Boder.Editor
{
    /// <summary>
    /// Editor tool for configuring URP settings for Boder game development
    /// Task: P1-002 - Unity Project Configuration
    /// </summary>
    public class URPConfigurationTool : EditorWindow
    {
        private UniversalRenderPipelineAsset _pcUrpAsset;
        private UniversalRenderPipelineAsset _mobileUrpAsset;

        [MenuItem("Boder/URP Configuration Tool")]
        public static void ShowWindow()
        {
            GetWindow<URPConfigurationTool>("URP Configuration");
        }

        private void OnEnable()
        {
            LoadURPAssets();
        }

        private void OnGUI()
        {
            GUILayout.Label("URP Configuration Tool", EditorStyles.boldLabel);
            GUILayout.Label("Task P1-002: Unity Project Configuration", EditorStyles.miniLabel);
            
            EditorGUILayout.Space();

            // URP Assets Section
            EditorGUILayout.LabelField("URP Assets", EditorStyles.boldLabel);
            
            _pcUrpAsset = (UniversalRenderPipelineAsset)EditorGUILayout.ObjectField(
                "PC URP Asset", _pcUrpAsset, typeof(UniversalRenderPipelineAsset), false);
                
            _mobileUrpAsset = (UniversalRenderPipelineAsset)EditorGUILayout.ObjectField(
                "Mobile URP Asset", _mobileUrpAsset, typeof(UniversalRenderPipelineAsset), false);

            EditorGUILayout.Space();

            // Current URP Settings
            DrawCurrentURPSettings();

            EditorGUILayout.Space();

            // Configuration Buttons
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Configure PC Settings"))
            {
                ConfigurePCSettings();
            }
            if (GUILayout.Button("Configure Mobile Settings"))
            {
                ConfigureMobileSettings();
            }
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Apply PC URP Asset"))
            {
                ApplyURPAsset(_pcUrpAsset);
            }
            if (GUILayout.Button("Apply Mobile URP Asset"))
            {
                ApplyURPAsset(_mobileUrpAsset);
            }
            GUILayout.EndHorizontal();

            EditorGUILayout.Space();

            if (GUILayout.Button("Validate URP Configuration"))
            {
                ValidateURPConfiguration();
            }
        }

        private void LoadURPAssets()
        {
            // Load existing URP assets
            _pcUrpAsset = AssetDatabase.LoadAssetAtPath<UniversalRenderPipelineAsset>(
                "Assets/Settings/PC_RPAsset.asset");
            _mobileUrpAsset = AssetDatabase.LoadAssetAtPath<UniversalRenderPipelineAsset>(
                "Assets/Settings/Mobile_RPAsset.asset");

            if (_pcUrpAsset == null)
            {
                Debug.LogWarning("[P1-002] PC URP Asset not found at expected path");
            }
            if (_mobileUrpAsset == null)
            {
                Debug.LogWarning("[P1-002] Mobile URP Asset not found at expected path");
            }
        }

        private void DrawCurrentURPSettings()
        {
            EditorGUILayout.LabelField("Current URP Settings", EditorStyles.boldLabel);
            
            var currentUrpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (currentUrpAsset != null)
            {
                EditorGUILayout.LabelField($"Asset Name: {currentUrpAsset.name}");
                EditorGUILayout.LabelField($"Render Scale: {currentUrpAsset.renderScale:F2}");
                EditorGUILayout.LabelField($"Shadow Distance: {currentUrpAsset.shadowDistance:F1}");
                EditorGUILayout.LabelField($"Shadow Cascades: {currentUrpAsset.shadowCascadeCount}");
                EditorGUILayout.LabelField($"Main Light Shadows: {currentUrpAsset.supportsMainLightShadows}");
                EditorGUILayout.LabelField($"Additional Light Shadows: {currentUrpAsset.supportsAdditionalLightShadows}");
            }
            else
            {
                EditorGUILayout.HelpBox("No URP Asset is currently active!", MessageType.Warning);
            }
        }

        private void ConfigurePCSettings()
        {
            if (_pcUrpAsset == null)
            {
                Debug.LogError("[P1-002] PC URP Asset is null!");
                return;
            }

            Debug.Log("[P1-002] Configuring PC URP settings...");

            // PC Optimized Settings
            var serializedObject = new SerializedObject(_pcUrpAsset);
            
            // Rendering Settings
            SetSerializedProperty(serializedObject, "m_RenderScale", 1.0f);
            SetSerializedProperty(serializedObject, "m_RequireDepthTexture", false);
            SetSerializedProperty(serializedObject, "m_RequireOpaqueTexture", false);
            
            // Shadow Settings
            SetSerializedProperty(serializedObject, "m_MainLightShadowsSupported", true);
            SetSerializedProperty(serializedObject, "m_AdditionalLightShadowsSupported", true);
            SetSerializedProperty(serializedObject, "m_ShadowDistance", 100f);
            SetSerializedProperty(serializedObject, "m_ShadowCascadeCount", 4);
            
            // Lighting Settings
            SetSerializedProperty(serializedObject, "m_AdditionalLightsRenderingMode", 1); // Per Pixel
            SetSerializedProperty(serializedObject, "m_AdditionalLightsPerObjectLimit", 4);
            
            // Post Processing
            SetSerializedProperty(serializedObject, "m_ColorGradingMode", 0); // High Dynamic Range
            SetSerializedProperty(serializedObject, "m_ColorGradingLutSize", 32);
            
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(_pcUrpAsset);
            
            Debug.Log("[P1-002] PC URP settings configured successfully");
        }

        private void ConfigureMobileSettings()
        {
            if (_mobileUrpAsset == null)
            {
                Debug.LogError("[P1-002] Mobile URP Asset is null!");
                return;
            }

            Debug.Log("[P1-002] Configuring Mobile URP settings...");

            // Mobile Optimized Settings
            var serializedObject = new SerializedObject(_mobileUrpAsset);
            
            // Rendering Settings
            SetSerializedProperty(serializedObject, "m_RenderScale", 0.75f);
            SetSerializedProperty(serializedObject, "m_RequireDepthTexture", false);
            SetSerializedProperty(serializedObject, "m_RequireOpaqueTexture", false);
            
            // Shadow Settings
            SetSerializedProperty(serializedObject, "m_MainLightShadowsSupported", true);
            SetSerializedProperty(serializedObject, "m_AdditionalLightShadowsSupported", false);
            SetSerializedProperty(serializedObject, "m_ShadowDistance", 25f);
            SetSerializedProperty(serializedObject, "m_ShadowCascadeCount", 2);
            
            // Lighting Settings
            SetSerializedProperty(serializedObject, "m_AdditionalLightsRenderingMode", 0); // Per Vertex
            SetSerializedProperty(serializedObject, "m_AdditionalLightsPerObjectLimit", 2);
            
            // Post Processing
            SetSerializedProperty(serializedObject, "m_ColorGradingMode", 1); // Low Dynamic Range
            SetSerializedProperty(serializedObject, "m_ColorGradingLutSize", 16);
            
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(_mobileUrpAsset);
            
            Debug.Log("[P1-002] Mobile URP settings configured successfully");
        }

        private void SetSerializedProperty(SerializedObject serializedObject, string propertyName, object value)
        {
            var property = serializedObject.FindProperty(propertyName);
            if (property != null)
            {
                switch (value)
                {
                    case float floatValue:
                        property.floatValue = floatValue;
                        break;
                    case int intValue:
                        property.intValue = intValue;
                        break;
                    case bool boolValue:
                        property.boolValue = boolValue;
                        break;
                }
            }
            else
            {
                Debug.LogWarning($"[P1-002] Property '{propertyName}' not found in URP Asset");
            }
        }

        private void ApplyURPAsset(UniversalRenderPipelineAsset urpAsset)
        {
            if (urpAsset == null)
            {
                Debug.LogError("[P1-002] Cannot apply null URP Asset!");
                return;
            }

            Debug.Log($"[P1-002] Applying URP Asset: {urpAsset.name}");
            
            GraphicsSettings.renderPipelineAsset = urpAsset;
            QualitySettings.renderPipeline = urpAsset;
            
            Debug.Log($"[P1-002] URP Asset '{urpAsset.name}' applied successfully");
        }

        private void ValidateURPConfiguration()
        {
            Debug.Log("[P1-002] Validating URP configuration...");
            
            bool isValid = true;
            
            // Check if URP is active
            var currentUrpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (currentUrpAsset == null)
            {
                Debug.LogError("[P1-002] No URP Asset is currently active!");
                isValid = false;
            }
            
            // Check if URP assets exist
            if (_pcUrpAsset == null)
            {
                Debug.LogError("[P1-002] PC URP Asset is missing!");
                isValid = false;
            }
            
            if (_mobileUrpAsset == null)
            {
                Debug.LogError("[P1-002] Mobile URP Asset is missing!");
                isValid = false;
            }
            
            // Check URP Global Settings
            var globalSettings = AssetDatabase.LoadAssetAtPath<UniversalRenderPipelineGlobalSettings>(
                "Assets/Settings/UniversalRenderPipelineGlobalSettings.asset");
            if (globalSettings == null)
            {
                Debug.LogError("[P1-002] URP Global Settings asset is missing!");
                isValid = false;
            }
            
            if (isValid)
            {
                Debug.Log("[P1-002] URP configuration validation passed!");
                EditorUtility.DisplayDialog("URP Validation Passed", 
                    "URP configuration is valid and optimized for target platforms.", "OK");
            }
            else
            {
                Debug.LogError("[P1-002] URP configuration validation failed!");
                EditorUtility.DisplayDialog("URP Validation Failed", 
                    "URP configuration has issues. Check the console for details.", "OK");
            }
        }
    }
}
