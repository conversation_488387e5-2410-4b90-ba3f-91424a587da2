{"name": "Boder.Runtime", "rootNamespace": "<PERSON><PERSON>", "references": ["Nappin.PhysicsCharacterController", "Unity.Cinemachine", "Unity.InputSystem", "Unity.TextMeshPro", "Unity.RenderPipelines.Universal.Runtime"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}