{"name": "Nappin.PhysicsCharacterController.Editor", "rootNamespace": "Nappin.Editor", "references": ["Nappin.PhysicsCharacterController", "Unity.Cinemachine", "Unity.InputSystem"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}