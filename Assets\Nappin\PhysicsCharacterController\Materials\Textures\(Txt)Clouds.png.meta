fileFormatVersion: 2
guid: 7afa741c2a3472044adc813df792603d
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 780861301130411774
    second: (Txt)Clouds_0
  - first:
      213: 5567312127037909479
    second: (Txt)Clouds_1
  - first:
      213: -8153460599520189410
    second: (Txt)Clouds_2
  - first:
      213: -5464868948980355734
    second: (Txt)Clouds_3
  - first:
      213: -1682327330900427241
    second: (Txt)Clouds_4
  - first:
      213: 8573202397875787090
    second: (Txt)Clouds_5
  externalObjects: {}
  serializedVersion: 11
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: -1
    mipBias: -100
    wrapU: 1
    wrapV: 1
    wrapW: -1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 1
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  applyGammaDecoding: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: (Txt)Clouds_0
      rect:
        serializedVersion: 2
        x: 50
        y: 157
        width: 119
        height: 121
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: efe3ab8573d26da00800000000000000
      internalID: 780861301130411774
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: (Txt)Clouds_1
      rect:
        serializedVersion: 2
        x: 171
        y: 157
        width: 113
        height: 119
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7e9a1d5f1e0134d40800000000000000
      internalID: 5567312127037909479
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: (Txt)Clouds_2
      rect:
        serializedVersion: 2
        x: 10
        y: 51
        width: 89
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e14a47b98c619de80800000000000000
      internalID: -8153460599520189410
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: (Txt)Clouds_3
      rect:
        serializedVersion: 2
        x: 108
        y: 54
        width: 70
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a6da6ff15a2e824b0800000000000000
      internalID: -5464868948980355734
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: (Txt)Clouds_4
      rect:
        serializedVersion: 2
        x: 183
        y: 69
        width: 53
        height: 53
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 71ebfb5b43c27a8e0800000000000000
      internalID: -1682327330900427241
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: (Txt)Clouds_5
      rect:
        serializedVersion: 2
        x: 245
        y: 64
        width: 59
        height: 59
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 255d9e878222af670800000000000000
      internalID: 8573202397875787090
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: bb70a29c154ea9f40a0c406acece8f9f
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 203438
  packageName: Physics Character Controller
  packageVersion: 5.0.0
  assetPath: Assets/Nappin/PhysicsCharacterController/Materials/Textures/(Txt)Clouds.png
  uploadId: 657120
