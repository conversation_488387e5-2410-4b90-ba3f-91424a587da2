﻿using UnityEngine;


namespace PhysicsCharacterController
{
    public class ToggleController : MonoB<PERSON><PERSON><PERSON>
    {
        [Header("Camera specs")]
        public GameObject gamepadCamera;
        public GameObject mouseAndKeyboardCamera;


        /**/


        public void isInputGamepad(bool _status)
        {
            gamepadCamera.SetActive(_status);
            mouseAndKeyboardCamera.SetActive(!_status);
        }
    }
}