/*
 * Boder Game Development
 * File: ProjectValidationTool.cs
 * Author: Lead Programmer
 * Created: [Current Date]
 * Task: P1-002
 * Description: Comprehensive project validation tool
 */

using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering.Universal;
using System.Collections.Generic;
using System.IO;

namespace Boder.Editor
{
    /// <summary>
    /// Comprehensive validation tool for Boder project configuration
    /// Task: P1-002 - Unity Project Configuration
    /// </summary>
    public class ProjectValidationTool : EditorWindow
    {
        private Vector2 _scrollPosition;
        private List<ValidationResult> _validationResults = new List<ValidationResult>();

        private struct ValidationResult
        {
            public string category;
            public string message;
            public MessageType type;
            public bool isValid;
        }

        [MenuItem("Boder/Project Validation Tool")]
        public static void ShowWindow()
        {
            GetWindow<ProjectValidationTool>("Project Validation");
        }

        private void OnGUI()
        {
            GUILayout.Label("Project Validation Tool", EditorStyles.boldLabel);
            GUILayout.Label("Task P1-002: Unity Project Configuration", EditorStyles.miniLabel);
            
            EditorGUILayout.Space();

            if (GUILayout.Button("Run Complete Validation"))
            {
                RunCompleteValidation();
            }

            EditorGUILayout.Space();

            // Display validation results
            if (_validationResults.Count > 0)
            {
                EditorGUILayout.LabelField("Validation Results:", EditorStyles.boldLabel);
                
                _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
                
                foreach (var result in _validationResults)
                {
                    EditorGUILayout.BeginHorizontal();
                    
                    // Category label
                    GUILayout.Label($"[{result.category}]", GUILayout.Width(120));
                    
                    // Message with appropriate styling
                    var style = result.type == MessageType.Error ? EditorStyles.label : EditorStyles.label;
                    var color = result.type == MessageType.Error ? Color.red : 
                               result.type == MessageType.Warning ? Color.yellow : Color.green;
                    
                    var originalColor = GUI.color;
                    GUI.color = color;
                    GUILayout.Label(result.message);
                    GUI.color = originalColor;
                    
                    EditorGUILayout.EndHorizontal();
                }
                
                EditorGUILayout.EndScrollView();
                
                // Summary
                int errors = 0, warnings = 0, passed = 0;
                foreach (var result in _validationResults)
                {
                    switch (result.type)
                    {
                        case MessageType.Error: errors++; break;
                        case MessageType.Warning: warnings++; break;
                        case MessageType.Info: passed++; break;
                    }
                }
                
                EditorGUILayout.Space();
                EditorGUILayout.LabelField($"Summary: {passed} Passed, {warnings} Warnings, {errors} Errors", EditorStyles.boldLabel);
            }
        }

        private void RunCompleteValidation()
        {
            Debug.Log("[P1-002] Running complete project validation...");
            
            _validationResults.Clear();
            
            ValidateUnityVersion();
            ValidatePackages();
            ValidatePlayerSettings();
            ValidateGraphicsSettings();
            ValidateQualitySettings();
            ValidateBuildSettings();
            ValidateProjectStructure();
            ValidateAssets();
            
            Debug.Log($"[P1-002] Validation completed. Results: {_validationResults.Count} checks performed");
            
            // Show summary dialog
            int errors = 0, warnings = 0;
            foreach (var result in _validationResults)
            {
                if (result.type == MessageType.Error) errors++;
                else if (result.type == MessageType.Warning) warnings++;
            }
            
            string message = $"Validation completed!\n\nErrors: {errors}\nWarnings: {warnings}\nTotal checks: {_validationResults.Count}";
            EditorUtility.DisplayDialog("Validation Complete", message, "OK");
        }

        private void ValidateUnityVersion()
        {
            var unityVersion = Application.unityVersion;
            
            if (unityVersion.StartsWith("6000.0"))
            {
                AddResult("Unity Version", $"Unity {unityVersion} - Compatible", MessageType.Info, true);
            }
            else if (unityVersion.StartsWith("2023.3"))
            {
                AddResult("Unity Version", $"Unity {unityVersion} - Recommended LTS", MessageType.Info, true);
            }
            else
            {
                AddResult("Unity Version", $"Unity {unityVersion} - May have compatibility issues", MessageType.Warning, false);
            }
        }

        private void ValidatePackages()
        {
            // Check required packages
            var requiredPackages = new Dictionary<string, string>
            {
                { "com.unity.render-pipelines.universal", "URP" },
                { "com.unity.inputsystem", "Input System" },
                { "com.unity.cinemachine", "Cinemachine" },
                { "com.unity.test-framework", "Test Framework" }
            };

            foreach (var package in requiredPackages)
            {
                var packageInfo = UnityEditor.PackageManager.PackageInfo.FindForAssetPath($"Packages/{package.Key}");
                if (packageInfo != null)
                {
                    AddResult("Packages", $"{package.Value} - Installed (v{packageInfo.version})", MessageType.Info, true);
                }
                else
                {
                    AddResult("Packages", $"{package.Value} - Missing", MessageType.Error, false);
                }
            }
        }

        private void ValidatePlayerSettings()
        {
            // Company Name
            if (string.IsNullOrEmpty(PlayerSettings.companyName))
            {
                AddResult("Player Settings", "Company name not set", MessageType.Warning, false);
            }
            else
            {
                AddResult("Player Settings", $"Company: {PlayerSettings.companyName}", MessageType.Info, true);
            }

            // Product Name
            if (string.IsNullOrEmpty(PlayerSettings.productName))
            {
                AddResult("Player Settings", "Product name not set", MessageType.Warning, false);
            }
            else
            {
                AddResult("Player Settings", $"Product: {PlayerSettings.productName}", MessageType.Info, true);
            }

            // Color Space
            if (PlayerSettings.colorSpace == ColorSpace.Linear)
            {
                AddResult("Player Settings", "Color Space: Linear (Recommended)", MessageType.Info, true);
            }
            else
            {
                AddResult("Player Settings", "Color Space: Gamma (Consider Linear for better quality)", MessageType.Warning, false);
            }

            // Scripting Backend
            var buildTargetGroup = BuildPipeline.GetBuildTargetGroup(EditorUserBuildSettings.activeBuildTarget);
            var scriptingBackend = PlayerSettings.GetScriptingBackend(buildTargetGroup);
            
            if (scriptingBackend == ScriptingImplementation.IL2CPP)
            {
                AddResult("Player Settings", "Scripting Backend: IL2CPP (Recommended)", MessageType.Info, true);
            }
            else
            {
                AddResult("Player Settings", $"Scripting Backend: {scriptingBackend} (Consider IL2CPP)", MessageType.Warning, false);
            }
        }

        private void ValidateGraphicsSettings()
        {
            // URP Asset
            var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset != null)
            {
                AddResult("Graphics", $"URP Asset: {urpAsset.name}", MessageType.Info, true);
                
                // Check render scale
                if (urpAsset.renderScale >= 0.75f && urpAsset.renderScale <= 1.0f)
                {
                    AddResult("Graphics", $"Render Scale: {urpAsset.renderScale:F2} (Good)", MessageType.Info, true);
                }
                else
                {
                    AddResult("Graphics", $"Render Scale: {urpAsset.renderScale:F2} (Check if appropriate)", MessageType.Warning, false);
                }
            }
            else
            {
                AddResult("Graphics", "URP Asset not configured", MessageType.Error, false);
            }
        }

        private void ValidateQualitySettings()
        {
            var qualityLevel = QualitySettings.GetQualityLevel();
            var qualityName = QualitySettings.names[qualityLevel];
            
            AddResult("Quality", $"Current Quality: {qualityName}", MessageType.Info, true);
            
            // VSync
            if (QualitySettings.vSyncCount == 1)
            {
                AddResult("Quality", "VSync: Enabled (Good for development)", MessageType.Info, true);
            }
            else
            {
                AddResult("Quality", $"VSync: {QualitySettings.vSyncCount} (Consider enabling for development)", MessageType.Warning, false);
            }
        }

        private void ValidateBuildSettings()
        {
            var activePlatform = EditorUserBuildSettings.activeBuildTarget;
            AddResult("Build Settings", $"Active Platform: {activePlatform}", MessageType.Info, true);
            
            // Development build
            if (EditorUserBuildSettings.development)
            {
                AddResult("Build Settings", "Development Build: Enabled (Good for development)", MessageType.Info, true);
            }
            else
            {
                AddResult("Build Settings", "Development Build: Disabled (Enable for development)", MessageType.Warning, false);
            }
            
            // Scenes in build
            var scenes = EditorBuildSettings.scenes;
            if (scenes.Length > 0)
            {
                AddResult("Build Settings", $"Scenes in build: {scenes.Length}", MessageType.Info, true);
            }
            else
            {
                AddResult("Build Settings", "No scenes in build settings", MessageType.Warning, false);
            }
        }

        private void ValidateProjectStructure()
        {
            // Check essential folders
            var requiredFolders = new string[]
            {
                "Assets/Scripts",
                "Assets/Scripts/Core",
                "Assets/Scripts/Player",
                "Assets/Scripts/UI",
                "Assets/Scripts/Audio",
                "Assets/Scripts/Editor",
                "Assets/Scenes",
                "Assets/Prefabs",
                "Assets/Materials",
                "Assets/Settings"
            };

            foreach (var folder in requiredFolders)
            {
                if (Directory.Exists(folder))
                {
                    AddResult("Project Structure", $"Folder exists: {folder}", MessageType.Info, true);
                }
                else
                {
                    AddResult("Project Structure", $"Missing folder: {folder}", MessageType.Warning, false);
                }
            }
        }

        private void ValidateAssets()
        {
            // Check for URP assets
            var urpAssets = new string[]
            {
                "Assets/Settings/PC_RPAsset.asset",
                "Assets/Settings/Mobile_RPAsset.asset",
                "Assets/Settings/UniversalRenderPipelineGlobalSettings.asset"
            };

            foreach (var asset in urpAssets)
            {
                if (File.Exists(asset))
                {
                    AddResult("Assets", $"URP Asset exists: {Path.GetFileName(asset)}", MessageType.Info, true);
                }
                else
                {
                    AddResult("Assets", $"Missing URP Asset: {Path.GetFileName(asset)}", MessageType.Warning, false);
                }
            }

            // Check for Input System assets
            if (File.Exists("Assets/InputSystem_Actions.inputactions"))
            {
                AddResult("Assets", "Input System Actions: Found", MessageType.Info, true);
            }
            else
            {
                AddResult("Assets", "Input System Actions: Missing", MessageType.Warning, false);
            }
        }

        private void AddResult(string category, string message, MessageType type, bool isValid)
        {
            _validationResults.Add(new ValidationResult
            {
                category = category,
                message = message,
                type = type,
                isValid = isValid
            });
        }
    }
}
